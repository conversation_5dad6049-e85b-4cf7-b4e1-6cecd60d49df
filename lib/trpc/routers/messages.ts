import { z } from "zod";
import { createTR<PERSON><PERSON>outer as router, protectedProcedure } from "../trpc";
import {
	chats,
	chatParticipants,
	messages,
	users,
	cats,
	catImages,
} from "@/lib/db/schema";
import { eq, and, desc, count, inArray, lt, not } from "drizzle-orm";
import { TRPCError } from "@trpc/server";
import { logSlowQuery, getPerformanceStats } from "./helpers/cat-helpers";
import { chatHelpers } from "./helpers/chat-helpers";

// Schema for creating a chat
const createChatSchema = z.object({
	recipientId: z.string(),
	catId: z.string().optional(),
	initialMessage: z.string().min(1, "Message cannot be empty"),
});

// Schema for sending a message
const sendMessageSchema = z.object({
	chatId: z.string(),
	content: z.string().min(1, "Message cannot be empty"),
});

// Schema for starting a conversation about a cat
const startConversationSchema = z.object({
	catId: z.string(),
	message: z.string().min(20, {
		message: "Your message must be at least 20 characters.",
	}),
});

// Schema for checking chat status for a specific cat
const checkChatStatusSchema = z.object({
	catId: z.string(),
});

export const messagesRouter = router({
	// Optimized query for ChatButton - only checks if conversation exists
	checkChatStatus: protectedProcedure
		.input(checkChatStatusSchema)
		.query(async ({ ctx, input }) => {
			const catId = parseInt(input.catId);
			const userId = parseInt(ctx.user.id);
			const startTime = performance.now();

			// Get cat owner info
			const catOwner = await chatHelpers.getCatOwnerInfo(ctx.db, catId);

			if (!catOwner) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is the cat owner
			if (catOwner.id === userId) {
				return {
					isOwner: true,
					chatExists: false,
					chatId: null,
					ownerName: null,
				};
			}

			// Check if conversation already exists
			const existingChat = await chatHelpers.findExistingChatForCat(
				ctx.db,
				catId,
				userId
			);
			const duration = performance.now() - startTime;
			logSlowQuery("checkChatStatus", duration);

			return {
				isOwner: false,
				chatExists: !!existingChat,
				chatId: existingChat?.id.toString() || null,
				ownerName: catOwner.name,
			};
		}),

	// Create a new chat
	createChat: protectedProcedure
		.input(createChatSchema)
		.mutation(async ({ ctx, input }) => {
			// Check if recipient exists
			const recipient = await ctx.db.query.users.findFirst({
				where: eq(users.id, parseInt(input.recipientId)),
			});

			if (!recipient) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Recipient not found",
				});
			}

			// Check if chat already exists between these users
			const existingChats = await ctx.db.query.chats.findMany({
				with: {
					participants: true,
				},
			});

			// Find a chat where both users are participants
			const existingChat = existingChats.find((chat) => {
				const participantIds = chat.participants.map((p) => p.userId);
				return (
					participantIds.includes(parseInt(ctx.user.id)) &&
					participantIds.includes(parseInt(input.recipientId))
				);
			});

			let chatId;

			if (existingChat) {
				// Use existing chat
				chatId = existingChat.id;
			} else {
				// Create a new chat
				const [newChat] = await ctx.db
					.insert(chats)
					.values({
						catId: input.catId ? parseInt(input.catId) : null,
					})
					.returning();

				if (!newChat) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to create chat",
					});
				}

				// Add both users to the chat
				await ctx.db.insert(chatParticipants).values([
					{
						chatId: newChat.id,
						userId: parseInt(ctx.user.id),
					},
					{
						chatId: newChat.id,
						userId: parseInt(input.recipientId),
					},
				]);

				chatId = newChat.id;
			}

			// Add the initial message
			await ctx.db.insert(messages).values({
				chatId,
				userId: parseInt(ctx.user.id),
				content: input.initialMessage,
			});

			return {
				chatId: chatId.toString(),
				message: "Chat created successfully",
			};
		}),

	// Start a conversation about adopting a cat
	startConversation: protectedProcedure
		.input(startConversationSchema)
		.mutation(async ({ ctx, input }) => {
			// Get the cat to check if it exists and is available
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, parseInt(input.catId)),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			if (cat.adopted) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "This cat has already been adopted",
				});
			}

			// Check if user is trying to adopt their own cat
			if (cat.userId === parseInt(ctx.user.id)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "You cannot adopt your own cat",
				});
			}

			// Check if user already has a conversation for this cat using helper
			const existingChat = await chatHelpers.findExistingChatForCat(
				ctx.db,
				parseInt(input.catId),
				parseInt(ctx.user.id)
			);

			// If chat exists and user is a participant, return that chat
			if (existingChat) {
				// Add a new message to the existing chat
				await ctx.db.insert(messages).values({
					chatId: existingChat.id,
					userId: parseInt(ctx.user.id),
					content: input.message,
				});

				return {
					chatId: existingChat.id,
					message: "Message sent successfully",
				};
			}

			// Create a new chat for this conversation
			const [newChat] = await ctx.db
				.insert(chats)
				.values({
					catId: parseInt(input.catId),
				})
				.returning();

			if (!newChat) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create chat",
				});
			}

			// Add both users to the chat
			await ctx.db.insert(chatParticipants).values([
				{
					chatId: newChat.id,
					userId: parseInt(ctx.user.id),
				},
				{
					chatId: newChat.id,
					userId: cat.userId,
				},
			]);

			// Add the initial message
			await ctx.db.insert(messages).values({
				chatId: newChat.id,
				userId: parseInt(ctx.user.id),
				content: input.message,
			});

			return {
				chatId: newChat.id,
				message: "Message sent successfully",
			};
		}),

	// Get all chats for the current user (optimized version)
	getMyChats: protectedProcedure
		.input(
			z
				.object({
					limit: z.number().default(20),
					offset: z.number().default(0),
				})
				.optional()
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { limit = 20, offset = 0 } = input || {};
			const querySteps: string[] = [];

			// Step 1: Get user's chat IDs efficiently using helper
			const step1Start = performance.now();
			const userChatIds = await chatHelpers.getUserChatIds(
				ctx.db,
				parseInt(ctx.user.id),
				limit,
				offset
			);
			querySteps.push(
				`Get user chat IDs: ${(performance.now() - step1Start).toFixed(2)}ms`
			);

			if (userChatIds.length === 0) {
				const duration = performance.now() - startTime;
				logSlowQuery("getMyChats", duration, 1000);
				return [];
			}

			const chatIds = userChatIds.map((c: any) => c.chatId);

			// Step 2: Get chat details with optimized joins
			const step2Start = performance.now();
			const chatsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					// Only fetch primary cat image
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
							},
						},
					},
					// Get other participants (excluding current user)
					participants: {
						where: (participants) =>
							and(eq(participants.userId, parseInt(ctx.user.id))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
					// Get only the latest message
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});
			querySteps.push(
				`Get chat details: ${(performance.now() - step2Start).toFixed(2)}ms`
			);

			// Step 3: Get other participants for each chat using helper
			const step3Start = performance.now();
			const otherParticipants = await chatHelpers.getChatParticipants(
				ctx.db,
				chatIds,
				parseInt(ctx.user.id)
			);
			querySteps.push(
				`Get other participants: ${(performance.now() - step3Start).toFixed(2)}ms`
			);

			// Step 4: Get unread message counts for all chats
			const step4Start = performance.now();
			const unreadCountsMap =
				await chatHelpers.getBatchUnreadMessageCounts(
					ctx.db,
					chatIds,
					parseInt(ctx.user.id)
				);
			querySteps.push(
				`Get unread counts: ${(performance.now() - step4Start).toFixed(2)}ms`
			);

			// Step 5: Get verification status for all other participants
			const step5Start = performance.now();
			const otherUserIds = otherParticipants.map((p: any) => p.user.id);
			const verificationMap =
				await chatHelpers.getBatchUserVerificationStatus(
					ctx.db,
					otherUserIds
				);
			querySteps.push(
				`Get verification status: ${(performance.now() - step5Start).toFixed(2)}ms`
			);

			// Create a map for quick lookup of other participants
			const otherParticipantsMap = new Map();
			otherParticipants.forEach((participant: any) => {
				otherParticipantsMap.set(participant.chatId, participant);
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getMyChats", duration, 1000);

			// Step 6: Format the response efficiently
			return chatsData
				.map((chat) => {
					const otherParticipant = otherParticipantsMap.get(chat.id);
					const lastMessage = chat.messages[0];
					const primaryImage = chat.cat?.images?.[0];
					const unreadCount = unreadCountsMap.get(chat.id) || 0;
					const isVerified = otherParticipant
						? verificationMap.get(otherParticipant.user.id) || false
						: false;

					return {
						id: chat.id.toString(),
						cat: chat.cat
							? {
									id: chat.cat.id.toString(),
									slug: chat.cat.slug,
									name: chat.cat.name,
									imageUrl:
										primaryImage?.url ||
										"/cat.jpeg?height=300&width=400",
								}
							: null,
						with: otherParticipant
							? {
									id: otherParticipant.user.id.toString(),
									slug: otherParticipant.user.slug,
									name: otherParticipant.user.name || "User",
									image: otherParticipant.user.image,
									role: otherParticipant.user.role,
									isVerified,
								}
							: null,
						lastMessage: lastMessage
							? {
									text: lastMessage.content,
									timestamp:
										lastMessage.createdAt.toISOString(),
									isRead: lastMessage.status === "read",
									isFromMe:
										lastMessage.userId ===
										parseInt(ctx.user.id),
								}
							: null,
						unreadCount,
						createdAt: chat.createdAt,
					};
				})
				.sort((a, b) => {
					// Sort by last message timestamp (newest first)
					const aTime =
						a.lastMessage?.timestamp || a.createdAt.toISOString();
					const bTime =
						b.lastMessage?.timestamp || b.createdAt.toISOString();
					return (
						new Date(bTime).getTime() - new Date(aTime).getTime()
					);
				});
		}),

	// Get all chats for the current user's cats (as owner)
	getChatsByOwner: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Get cats owned by the user
			const userCats = await ctx.db.query.cats.findMany({
				where: eq(cats.userId, parseInt(ctx.user.id)),
			});

			if (!userCats.length) {
				return {
					chats: [],
					pagination: {
						total: 0,
						pageCount: 0,
						page: input.page,
						limit: input.limit,
					},
				};
			}

			const catIds = userCats.map((cat) => cat.id);

			// Get chats for these cats
			const chatsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.catId, catIds),
				with: {
					cat: {
						with: {
							images: true,
						},
					},
					participants: {
						with: {
							user: true,
						},
						where: (participants) =>
							and(eq(participants.userId, parseInt(ctx.user.id))),
					},
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
					},
				},
				orderBy: desc(chats.createdAt),
				limit: input.limit,
				offset: (input.page - 1) * input.limit,
			});

			// Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chats)
				.where(inArray(chats.catId, catIds));

			const duration = performance.now() - startTime;
			logSlowQuery("getChatsByOwner", duration);

			// Format the response
			return {
				chats: chatsData.map((chat) => {
					// Find the other participant (not the current user)
					const otherParticipants = chat.participants.filter(
						(p) => p.userId !== parseInt(ctx.user.id)
					);

					return {
						id: chat.id.toString(),
						catId: chat.catId?.toString(),
						catSlug: chat.cat?.slug,
						catName: chat.cat?.name || "Unknown Cat",
						catImage:
							chat.cat?.images?.find((img) => img.isPrimary)
								?.url ||
							chat.cat?.images?.[0]?.url ||
							"/cat.jpeg?height=300&width=400",
						lastMessage: chat.messages[0]?.content || "",
						lastMessageDate:
							chat.messages[0]?.createdAt || chat.createdAt,
						participants: otherParticipants.map((p) => ({
							id: p.user.id.toString(),
							slug: p.user.slug,
							name: p.user.name,
							image: p.user.image,
						})),
					};
				}),
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get all chats the current user is participating in (as potential adopter)
	getMyChatsByParticipant: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			// Get chats where the user is a participant
			const userChats = await ctx.db.query.chatParticipants.findMany({
				where: eq(chatParticipants.userId, parseInt(ctx.user.id)),
				with: {
					chat: {
						with: {
							cat: {
								with: {
									images: true,
									user: true,
								},
							},
							participants: {
								with: {
									user: true,
								},
							},
							messages: {
								orderBy: desc(messages.createdAt),
								limit: 1,
							},
						},
					},
				},
				orderBy: desc(chatParticipants.createdAt),
				limit: input.limit,
				offset: (input.page - 1) * input.limit,
			});

			// Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, parseInt(ctx.user.id)));

			// Format the response
			return {
				chats: userChats.map((chatParticipant) => {
					const chat = chatParticipant.chat;

					// Find the cat owner (not the current user)
					const catOwner = chat.cat?.user;

					return {
						id: chat.id.toString(),
						catId: chat.catId?.toString(),
						catSlug: chat.cat?.slug,
						catName: chat.cat?.name || "Unknown Cat",
						catImage:
							chat.cat?.images?.find((img) => img.isPrimary)
								?.url ||
							chat.cat?.images?.[0]?.url ||
							"/cat.jpeg?height=300&width=400",
						lastMessage: chat.messages[0]?.content || "",
						lastMessageDate:
							chat.messages[0]?.createdAt || chat.createdAt,
						owner: catOwner
							? {
									id: catOwner.id.toString(),
									slug: catOwner.slug,
									name: catOwner.name,
									image: catOwner.image,
								}
							: null,
					};
				}),
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get messages for a specific chat (optimized version)
	getChatMessages: protectedProcedure
		.input(
			z.object({
				chatId: z.string(),
				limit: z.number().min(1).max(100).default(50),
				cursor: z.string().optional(), // For pagination
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const chatId = parseInt(input.chatId);
			const userId = parseInt(ctx.user.id);

			// Step 1: Validate chat access using optimized helper (single query)
			const accessValidation = await chatHelpers.validateChatAccess(
				ctx.db,
				chatId,
				userId
			);

			if (!accessValidation.valid) {
				throw new TRPCError({
					code:
						accessValidation.error === "Chat not found"
							? "NOT_FOUND"
							: "FORBIDDEN",
					message: accessValidation.error,
				});
			}

			// Step 2: Get optimized chat details with selective field fetching
			const chat = await ctx.db.query.chats.findFirst({
				where: eq(chats.id, chatId),
				columns: {
					id: true,
					catId: true,
					createdAt: true,
				},
				with: {
					// Only fetch essential participant info
					participants: {
						columns: {
							userId: true,
						},
						with: {
							user: {
								columns: {
									id: true,
									name: true,
								},
							},
						},
					},
				},
			});

			if (!chat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Chat not found",
				});
			}

			// Step 3: Get messages with optimized query and selective user data
			const baseWhere = eq(messages.chatId, chatId);
			const messagesWhere = input.cursor
				? and(baseWhere, lt(messages.createdAt, new Date(input.cursor)))
				: baseWhere;

			const chatMessages = await ctx.db.query.messages.findMany({
				where: messagesWhere,
				orderBy: desc(messages.createdAt),
				limit: input.limit,
				columns: {
					id: true,
					content: true,
					createdAt: true,
					status: true,
					userId: true,
				},
				with: {
					user: {
						columns: {
							id: true,
							name: true,
						},
					},
				},
			});

			// Step 4: Batch update unread messages (if any)
			const unreadMessageIds = chatMessages
				.filter((msg) => msg.userId !== userId && msg.status !== "read")
				.map((msg) => msg.id);

			// Use Promise.all for concurrent operations
			const updatePromise =
				unreadMessageIds.length > 0
					? ctx.db
							.update(messages)
							.set({ status: "read" })
							.where(inArray(messages.id, unreadMessageIds))
					: Promise.resolve();

			// Execute update concurrently with response formatting
			await updatePromise;

			// Step 5: Format response efficiently
			const otherParticipant = chat.participants.find(
				(p) => p.userId !== userId
			);

			const duration = performance.now() - startTime;
			logSlowQuery("getChatMessages", duration, 500);

			return {
				chat: {
					id: chat.id.toString(),
					with: otherParticipant
						? {
								id: otherParticipant.user.id.toString(),
								name: otherParticipant.user.name || "User",
							}
						: null,
				},
				messages: chatMessages.map((msg) => ({
					id: msg.id.toString(),
					content: msg.content,
					timestamp: msg.createdAt.toISOString(),
					status: msg.status,
					isFromMe: msg.userId === userId,
					sender: {
						id: msg.user.id.toString(),
						name: msg.user.name || "User",
					},
				})),
				nextCursor:
					chatMessages.length === input.limit
						? chatMessages[
								chatMessages.length - 1
							].createdAt.toISOString()
						: undefined,
			};
		}),

	// Send a message in a chat
	sendMessage: protectedProcedure
		.input(sendMessageSchema)
		.mutation(async ({ ctx, input }) => {
			const chatId = parseInt(input.chatId);
			const userId = parseInt(ctx.user.id);

			// Check if user is a participant in this chat using helper
			const isParticipant = await chatHelpers.isUserParticipant(
				ctx.db,
				chatId,
				userId
			);

			if (!isParticipant) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You are not a participant in this chat",
				});
			}

			// Send the message
			const [newMessage] = await ctx.db
				.insert(messages)
				.values({
					chatId,
					userId: parseInt(ctx.user.id),
					content: input.content,
				})
				.returning();

			if (!newMessage) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to send message",
				});
			}

			// Note: tRPC/React Query will handle cache invalidation automatically

			return {
				id: newMessage.id.toString(),
				content: newMessage.content,
				timestamp: newMessage.createdAt.toISOString(),
				status: newMessage.status,
			};
		}),

	// Get user's conversations for listing (optimized - no message content)
	getMyConversations: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const userId = parseInt(ctx.user.id);

			// Step 1: Get user's chat IDs efficiently
			const userChatIds = await ctx.db
				.select({ chatId: chatParticipants.chatId })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, userId))
				.orderBy(desc(chatParticipants.createdAt))
				.limit(input.limit)
				.offset((input.page - 1) * input.limit);

			if (userChatIds.length === 0) {
				return {
					conversations: [],
					pagination: {
						total: 0,
						pageCount: 0,
						page: input.page,
						limit: input.limit,
					},
				};
			}

			const chatIds = userChatIds.map((c) => c.chatId);

			// Step 2: Get conversation data with optimized joins
			const conversationsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					// Only fetch primary cat image
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
								columns: {
									url: true,
								},
							},
						},
					},
					// Get other participants (excluding current user)
					participants: {
						where: (participants) =>
							and(not(eq(participants.userId, userId))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
					// Get only the latest message for preview
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});

			// Step 3: Get unread message counts in batch
			const unreadCounts = await chatHelpers.getBatchUnreadMessageCounts(
				ctx.db,
				chatIds,
				userId
			);

			// Step 4: Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, userId));

			// Step 5: Format response efficiently
			const conversations = conversationsData.map((chat) => {
				const otherParticipant = chat.participants[0];
				const lastMessage = chat.messages[0];
				const unreadCount = unreadCounts.get(chat.id) || 0;

				return {
					id: chat.id.toString(),
					with: otherParticipant?.user
						? {
								id: otherParticipant.user.id.toString(),
								slug: otherParticipant.user.slug,
								name: otherParticipant.user.name,
								image: otherParticipant.user.image,
								isVerified:
									otherParticipant.user.role === "clinic" ||
									otherParticipant.user.role === "rescuer",
							}
						: null,
					cat: chat.cat
						? {
								id: chat.cat.id.toString(),
								slug: chat.cat.slug,
								name: chat.cat.name,
								imageUrl:
									chat.cat.images[0]?.url ||
									"/cat.jpeg?height=300&width=400",
							}
						: null,
					lastMessage: lastMessage
						? {
								text: lastMessage.content,
								timestamp: lastMessage.createdAt.toISOString(),
								isFromMe: lastMessage.userId === userId,
								isRead: lastMessage.status === "read",
							}
						: null,
					unreadCount,
				};
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getMyConversations", duration, 300);

			return {
				conversations,
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get conversation header information (optimized for header display)
	getConversationHeader: protectedProcedure
		.input(z.object({ chatId: z.string() }))
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const userId = parseInt(ctx.user.id);
			const chatId = parseInt(input.chatId);

			// Validate chat access
			const validation = await chatHelpers.validateChatAccess(
				ctx.db,
				chatId,
				userId
			);
			if (!validation.valid) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: validation.error,
				});
			}

			// Get conversation header data with minimal joins
			const conversationData = await ctx.db.query.chats.findFirst({
				where: eq(chats.id, chatId),
				with: {
					// Get cat info for header
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
							userId: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
								columns: {
									url: true,
								},
							},
						},
					},
					// Get other participant info
					participants: {
						where: (participants) =>
							and(not(eq(participants.userId, userId))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
				},
			});

			if (!conversationData) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Conversation not found",
				});
			}

			const otherParticipant = conversationData.participants[0];
			const duration = performance.now() - startTime;
			logSlowQuery("getConversationHeader", duration, 100);

			return {
				with: otherParticipant?.user
					? {
							id: otherParticipant.user.id.toString(),
							slug: otherParticipant.user.slug,
							name: otherParticipant.user.name,
							image: otherParticipant.user.image,
							isVerified:
								otherParticipant.user.role === "clinic" ||
								otherParticipant.user.role === "rescuer",
						}
					: null,
				cat: conversationData.cat
					? {
							id: conversationData.cat.id.toString(),
							slug: conversationData.cat.slug,
							name: conversationData.cat.name,
							imageUrl:
								conversationData.cat.images[0]?.url ||
								"/cat.jpeg?height=300&width=400",
							userId: conversationData.cat.userId?.toString(),
						}
					: null,
			};
		}),

	// Get performance statistics for monitoring (admin only)
	getPerformanceStats: protectedProcedure
		.input(z.object({ queryName: z.string().optional() }).optional())
		.query(async ({ ctx, input }) => {
			// Get user from database to check role
			const currentUser = await ctx.db.query.users.findFirst({
				where: eq(users.id, parseInt(ctx.user.id)),
			});

			// Only allow admin users to access performance stats
			if (!currentUser || currentUser.role !== "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only admin users can access performance statistics",
				});
			}

			const stats = getPerformanceStats(input?.queryName);

			return {
				timestamp: new Date().toISOString(),
				stats,
			};
		}),
});
